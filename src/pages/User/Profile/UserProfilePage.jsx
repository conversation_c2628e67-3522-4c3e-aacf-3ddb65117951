import React, { useState, useEffect, useContext } from "react";
import { useNavigate } from "react-router-dom";
import MkdSDK from "Utils/MkdSDK";
import { showToast, GlobalContext } from "Context/Global";



const UserProfilePage = () => {
  const navigate = useNavigate();
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [industries, setIndustries] = useState([]);
  const { dispatch: globalDispatch } = useContext(GlobalContext);

  useEffect(() => {
    loadProfileData();
    fetchIndustries();
  }, []);

  const fetchIndustries = async () => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI("/v1/api/dealmaker/industries", {}, "GET");

      if (!response.error && response.data) {
        setIndustries(response.data);
      } else {
        console.error('Error fetching industries:', response.message);
      }
    } catch (err) {
      console.error('Error in fetchIndustries:', err);
    }
  };

  const loadProfileData = async () => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI('/v1/api/dealmaker/user/details', {}, 'GET');

      if (!response.error) {
        setProfile(response.model);
      } else {
        showToast(globalDispatch, "Failed to fetch profile details", 5000, "error");
      }
    } catch (err) {
      console.error('Error fetching profile:', err);
      showToast(globalDispatch, "Failed to fetch profile details", 5000, "error");
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-[#1e1e1e]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#2e7d32]"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#1e1e1e] p-4 md:p-6">
      <div className="mb-6 flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-xl font-semibold text-[#eaeaea]">My Profile</h1>
          <p className="text-sm text-[#b5b5b5]">View and manage your profile information</p>
        </div>
        <button
          onClick={() => navigate('/member/profile/edit')}
          className="rounded-lg bg-[#2e7d32] px-4 py-2 text-[#eaeaea] hover:bg-[#1b5e20] transition-colors"
        >
          Edit Profile
        </button>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-[5fr_1fr]">
        {/* Main Profile Info */}
        <div className="space-y-6">
          <div className="rounded-lg border border-[#363636] bg-[#252525] p-6">
            <div className="mb-6 flex items-center gap-4">
              <div className="h-20 w-20 rounded-full bg-[#2e7d32] flex items-center justify-center">
                {profile?.photo?.value ? (
                  <img
                    src={profile.photo.value}
                    alt="Profile"
                    className="h-full w-full rounded-full object-cover"
                  />
                ) : (
                  <div className="text-2xl font-bold text-white">
                    {profile?.first_name?.value?.charAt(0)}
                  </div>
                )}
              </div>
              <div>
                <h2 className="text-2xl font-semibold text-[#eaeaea]">
                  {profile?.first_name?.value} {profile?.last_name?.value}
                </h2>
                <p className="text-[#b5b5b5]">ID: {profile?.id?.value}</p>
              </div>
            </div>

            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div>
                <h3 className="mb-2 text-sm font-medium text-[#b5b5b5]">Contact Information</h3>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm text-[#b5b5b5]">Email</p>
                    <p className="text-[#eaeaea]">{profile?.email?.value || 'Not provided'}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="rounded-lg border border-[#363636] bg-[#252525] p-6">
            <h3 className="mb-4 text-lg font-semibold text-[#eaeaea]">Professional Information</h3>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 mb-6">
              <div>
                <p className="text-sm text-[#b5b5b5]">Industry</p>
                <p className="text-[#eaeaea]">{(() => {
          const industryId = typeof profile?.industry_id?.value === 'string'
            ? parseInt(profile.industry_id.value, 10)
            : profile?.industry_id?.value;

          const industry = industries.find(ind => ind.id === industryId);
          return industry ? industry.name : 'Not specified';
        })()}</p>
              </div>
              <div>
                <p className="text-sm text-[#b5b5b5]">Payout Method</p>
                <p className="text-[#eaeaea]">{profile?.payout_method?.value || 'Not specified'}</p>
              </div>
            </div>

            <div>
              <p className="text-sm text-[#b5b5b5] mb-2">Industries I'm Interested In</p>
              {profile?.interested_industries?.value &&
               Array.isArray(profile.interested_industries.value) &&
               profile.interested_industries.value.length > 0 ? (
                <div className="flex flex-wrap gap-2">
                  {profile.interested_industries.value.map(industryId => {
                    // Parse the ID to an integer if it's a string
                    const parsedId = typeof industryId === 'string' ? parseInt(industryId, 10) : industryId;
                    const industry = INDUSTRY_OPTIONS.find(ind => ind.id === parsedId);
                    return industry ? (
                      <span
                        key={industry.id}
                        className="inline-block rounded-full bg-[#2e7d3233] px-3 py-1 text-sm text-[#7dd87d]"
                      >
                        {industry.name}
                      </span>
                    ) : null;
                  })}
                </div>
              ) : (
                <p className="text-[#eaeaea]">No industries specified</p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserProfilePage;